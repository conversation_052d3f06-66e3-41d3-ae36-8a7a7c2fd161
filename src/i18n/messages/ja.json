{"LanguageDetection": {"title": "言語の提案", "description": "ブラウザの言語が現在のサイトの言語と異なることが検出されました。いつでも言語を切り替えることができます 👉", "countdown": "{countdown} 秒後に閉じます"}, "Header": {"GitHub": "GitHub"}, "Footer": {"Copyright": "Copyright © {year} <PERSON>. All rights reserved.", "socialLinks": {"github": "GitHub リポジトリ", "homepage": "ホームページ", "twitter": "Twitter", "email": "E メール"}, "sections": {"languages": "言語", "openSource": "オープンソース", "legalPrivacy": "法務とプライバシー"}, "links": {"codeocr": "CodeOCR", "privacyPolicy": "プライバシーポリシー", "termsOfService": "利用規約"}}, "Home": {"title": "CodeOCR", "tagLine": "コードのスクリーンショットをコピー可能なテキストに変換", "description": "コードのスクリーンショットをアップロードし、高度なマルチモーダル AI モデルを使用してコピー可能なコードテキストに変換します。"}, "About": {"title": "概要", "description": "このサイトについて"}, "TermsOfService": {"title": "利用規約", "description": "利用規約"}, "PrivacyPolicy": {"title": "プライバシーポリシー", "description": "プライバシーポリシー"}, "ImageUpload": {"title": "コードのスクリーンショットをアップロード", "subtitle": "ペースト、ドロップ、またはクリックしてアップロード", "supportedFormats": "PNG, JPG, WebP", "maxSize": "最大 5MB", "errorInvalidFormat": "有効な画像ファイル（PNG、JPG、JPEG、WebP）をアップロードしてください", "errorFileSize": "ファイルサイズは 5MB 未満である必要があります"}, "CodeOCR": {"processing": "処理中...", "extractCode": "コードを抽出", "extractedCode": "抽出されたコード", "selectImageFirst": "最初に画像を選択してください", "apiKeyRequired": "API 設定で API キーを設定してください", "modelNameRequired": "API 設定でモデル名を設定してください", "extractSuccess": "コ��ドの抽出に成功しました！", "extractFailed": "画像の処理に失敗しました", "noCodeDetected": "コードが検出されませんでした"}, "ThemeToggle": {"toggleTheme": "テーマを切り替え", "light": "ライト", "dark": "ダーク", "system": "システム"}, "APISettings": {"title": "API 設定", "description": "コード抽出機能の API 設定を構成します。", "apiKey": "API キー", "apiKeyPlaceholder": "sk-xxx", "apiBase": "API ベース URL", "apiBasePlaceholder": "https://api.openai.com/v1", "modelName": "モデル名", "modelNamePlaceholder": "ビジョン LLM モデル名を入力してください", "save": "保存", "settingsSaved": "設定が正常に保存されました", "settingsFailed": "設定の保存に失敗しました"}}