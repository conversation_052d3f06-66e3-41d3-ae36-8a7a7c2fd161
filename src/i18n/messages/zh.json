{"LanguageDetection": {"title": "语言建议", "description": "我们注意到您的浏览器语言与当前网站语言不同。您可以随时切换语言 👉", "countdown": "{countdown} 秒后关闭"}, "Header": {"GitHub": "GitHub"}, "Footer": {"Copyright": "版权所有 © {year} <PERSON>。保留所有权利。", "socialLinks": {"github": "GitHub 仓库", "homepage": "个人主页", "twitter": "Twitter", "email": "电子邮件"}, "sections": {"languages": "语言", "openSource": "开源", "legalPrivacy": "法律和隐私"}, "links": {"codeocr": "CodeOCR", "privacyPolicy": "隐私政策", "termsOfService": "服务条款"}}, "Home": {"title": "CodeOCR", "tagLine": "将代码截图转换为可复制的文本", "description": "上传代码截图，使用先进的多模态 AI 模型将其转换为可复制的代码文本。"}, "About": {"title": "关于", "description": "关于本站"}, "TermsOfService": {"title": "服务条款", "description": "服务条款"}, "PrivacyPolicy": {"title": "隐私政策", "description": "隐私政策"}, "ImageUpload": {"title": "上传您的代码截图", "subtitle": "粘贴、拖放或点击上传", "supportedFormats": "支持 PNG, JPG, WebP", "maxSize": "最大 5MB", "errorInvalidFormat": "请上传有效的图片文件 (PNG, JPG, JPEG, WebP)", "errorFileSize": "文件大小必须小于 5MB"}, "CodeOCR": {"processing": "处理中...", "extractCode": "提取代码", "extractedCode": "提取的代码", "selectImageFirst": "请先选择一张图片", "apiKeyRequired": "请先在 API 设置中配置您的 API 密钥", "modelNameRequired": "请先在 API 设置中配置您的模型名称", "extractSuccess": "代码提取成功！", "extractFailed": "处理图片失败", "noCodeDetected": "未检测到代码"}, "ThemeToggle": {"toggleTheme": "切换主题", "light": "浅色", "dark": "深色", "system": "系统"}, "APISettings": {"title": "API 设置", "description": "为代码提取功能配置您的 API 设置。", "apiKey": "API 密钥", "apiKeyPlaceholder": "sk-xxx", "apiBase": "API Base URL", "apiBasePlaceholder": "https://api.openai.com/v1", "modelName": "模型名称", "modelNamePlaceholder": "输入一个视觉语言模型名称", "save": "保存", "settingsSaved": "设置已成功保存", "settingsFailed": "保存设置失败"}}