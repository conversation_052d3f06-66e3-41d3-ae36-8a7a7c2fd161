"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Settings } from "lucide-react";
import { useTranslations } from "next-intl";
import { useState, useEffect } from "react";

interface APISettings {
  apiKey: string;
  apiBase: string;
  modelName: string;
}

interface APISettingsDialogProps {
  onSettingsChange: (settings: APISettings) => void;
  initialSettings: APISettings;
}

export default function APISettingsDialog({
  onSettingsChange,
  initialSettings,
}: APISettingsDialogProps) {
  const t = useTranslations("APISettings");
  const [isOpen, setIsOpen] = useState(false);
  const [settings, setSettings] = useState<APISettings>(initialSettings);

  useEffect(() => {
    setSettings(initialSettings);
  }, [initialSettings]);

  const handleSave = () => {
    onSettingsChange(settings);
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="w-full">
          <Settings className="mr-2 h-4 w-4" />
          {t("title")}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{t("title")}</DialogTitle>
          <DialogDescription>{t("description")}</DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="api-key" className="text-right">
              {t("apiKey")}
            </Label>
            <Input
              id="api-key"
              type="password"
              value={settings.apiKey}
              onChange={(e) =>
                setSettings({ ...settings, apiKey: e.target.value })
              }
              className="col-span-3"
              placeholder={t("apiKeyPlaceholder")}
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="api-base" className="text-right">
              {t("apiBase")}
            </Label>
            <Input
              id="api-base"
              value={settings.apiBase}
              onChange={(e) =>
                setSettings({ ...settings, apiBase: e.target.value })
              }
              className="col-span-3"
              placeholder={t("apiBasePlaceholder")}
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="model-name" className="text-right">
              {t("modelName")}
            </Label>
            <Input
              id="model-name"
              value={settings.modelName}
              onChange={(e) =>
                setSettings({ ...settings, modelName: e.target.value })
              }
              className="col-span-3"
              placeholder={t("modelNamePlaceholder")}
            />
          </div>
        </div>
        <DialogFooter>
          <Button onClick={handleSave}>{t("save")}</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
