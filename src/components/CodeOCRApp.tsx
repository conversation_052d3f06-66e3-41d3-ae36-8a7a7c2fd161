"use client";

import APISettingsDialog from "@/components/APISettingsDialog";
import ImageUpload from "@/components/ImageUpload";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import type { BundledLanguage } from "@/components/ui/kibo-ui/code-block";
import {
  CodeBlock,
  CodeBlockBody,
  CodeBlockContent,
  CodeBlockCopyButton,
  CodeBlockFilename,
  CodeBlockFiles,
  CodeBlockHeader,
  CodeBlockItem,
} from "@/components/ui/kibo-ui/code-block";
import { Loader2, ScanText, Code } from "lucide-react";
import { useTranslations } from "next-intl";
import { useCallback, useState } from "react";
import { toast } from "sonner";

interface CodeOCRResponse {
  code: string;
}

interface APISettings {
  apiKey: string;
  apiBase: string;
  modelName: string;
}

export default function CodeOCRApp() {
  const t = useTranslations("CodeOCR");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [result, setResult] = useState<CodeOCRResponse | null>(null);
  const [apiSettings, setApiSettings] = useState<APISettings>({
    apiKey: "",
    apiBase: "",
    modelName: "gpt-4o",
  });

  const handleFileSelect = useCallback((file: File) => {
    setSelectedFile(file);
    setResult(null);
  }, []);

  const handleRemoveFile = useCallback(() => {
    setSelectedFile(null);
    setResult(null);
  }, []);

  const processImage = useCallback(async () => {
    if (!selectedFile) {
      toast.error(t("selectImageFirst"));
      return;
    }
    if (!apiSettings.apiKey) {
      toast.error(t("apiKeyRequired"));
      return;
    }
    if (!apiSettings.modelName) {
      toast.error(t("modelNameRequired"));
      return;
    }

    setIsProcessing(true);
    setResult(null);

    const reader = new FileReader();
    reader.readAsDataURL(selectedFile);
    reader.onload = async () => {
      const base64Image = reader.result as string;
      const body = JSON.stringify({
        image_source: base64Image,
        model_name: apiSettings.modelName,
        api_key: apiSettings.apiKey,
        api_base: apiSettings.apiBase,
      });

      try {
        const response = await fetch("/api/v1/codeocr", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body,
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data?.details || data?.error || t("extractFailed"));
        }

        setResult(data);
        toast.success(t("extractSuccess"));
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : t("extractFailed");
        toast.error(errorMessage);
        setResult({
          code: `\`\`\`text\n--- ERROR ---\n${errorMessage}\n\`\`\``,
        });
      } finally {
        setIsProcessing(false);
      }
    };
    reader.onerror = (error) => {
      toast.error(t("imageReadFailed"));
      setIsProcessing(false);
    };
  }, [selectedFile, apiSettings, t]);

  const parseCodeBlock = (rawCode: string | undefined) => {
    if (!rawCode) {
      return { language: "text", code: "" };
    }
    const match = rawCode.match(/^```(\w+)\n([\s\S]*)\n```$/);
    if (match) {
      return { language: match[1], code: match[2] };
    }
    return { language: "text", code: rawCode };
  };

  const { language, code } = parseCodeBlock(result?.code);

  return (
    <div className="w-full space-y-4">
      <Card className="shadow-md">
        <CardContent className="p-4 sm:p-6">
          <div className="space-y-4 sm:space-y-6">
            <ImageUpload
              onFileSelect={handleFileSelect}
              onRemoveFile={handleRemoveFile}
              selectedFile={selectedFile}
              disabled={isProcessing}
            />

            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
              <APISettingsDialog
                onSettingsChange={setApiSettings}
                initialSettings={apiSettings}
              />
              <Button
                onClick={processImage}
                disabled={!selectedFile || isProcessing}
                className="w-full sm:flex-1"
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {t("processing")}
                  </>
                ) : (
                  <>
                    <ScanText className="mr-2 h-4 w-4" />
                    {t("extractCode")}
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {result && code && (
        <CodeBlock
          data={[{ language, filename: "", code }]}
          defaultValue={language}
        >
          <CodeBlockHeader>
            <CodeBlockFiles>
              {(item) => (
                <CodeBlockFilename key={item.language} value={item.language}>
                  <div className="flex items-center gap-2">
                    <Code /> {t("extractedCode")}
                  </div>
                </CodeBlockFilename>
              )}
            </CodeBlockFiles>
            <CodeBlockCopyButton value={code} />
          </CodeBlockHeader>
          <CodeBlockBody>
            {(item) => (
              <CodeBlockItem key={item.language} value={item.language}>
                <CodeBlockContent language={item.language as BundledLanguage}>
                  {item.code}
                </CodeBlockContent>
              </CodeBlockItem>
            )}
          </CodeBlockBody>
        </CodeBlock>
      )}

      {result && !code && (
        <div className="border rounded-lg p-8 text-center text-gray-500 dark:text-gray-400">
          {t("noCodeDetected")}
        </div>
      )}
    </div>
  );
}
