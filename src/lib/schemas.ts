import { z } from 'zod';

/**
 * Defines the structure for a request to the CodeOCR API.
 */
export const CodeOcrRequestSchema = z.object({
  image_source: z.string().min(1, { message: 'Image source cannot be empty.' }),
  model_name: z.string().min(1, { message: 'Model name cannot be empty.' }),
  api_key: z.string().optional(),
  api_base: z.string().optional(),
});

/**
 * Defines the structured response for the Code OCR API.
 * The response contains a single 'code' field, which is a Markdown-formatted
 * code block.
 */
export const CodeOcrResponseSchema = z.object({
  code: z
    .string()
    .refine(
      (v) => {
        const trimmed = v.trim();
        return trimmed.startsWith('```') && trimmed.endsWith('```');
      },
      {
        message: 'Code must be enclosed in triple backticks (```).',
      }
    )
    .refine(
      (v) => {
        const match = v.trim().match(/^```(\w+)\n/);
        return match !== null && match[1] !== undefined && match[1].length > 0;
      },
      {
        message:
          'Invalid format. Must be ```language\n...code...\n```. Ensure there is a language identifier after the opening backticks and a newline.',
      }
    )
    .refine(
      (v) => {
        const content = v.trim().replace(/^```\w+\n/, '').replace(/```$/, '');
        return content.trim().length > 0;
      },
      {
        message: 'Code content inside the block cannot be empty.',
      }
    ),
});

// Export types for convenience
export type CodeOcrRequest = z.infer<typeof CodeOcrRequestSchema>;
export type CodeOcrResponse = z.infer<typeof CodeOcrResponseSchema>;
