import { NextResponse } from "next/server";
import { generateObject } from "ai";
import { CodeOcrRequestSchema, CodeOcrResponseSchema } from "@/lib/schemas";
import { createOpenAI } from "@ai-sdk/openai";
import { CODE_OCR_PROMPT } from "@/lib/prompts";

export const runtime = "edge";

export async function POST(req: Request) {
  try {
    // 1. Validate request body
    const body = await req.json();
    const validation = CodeOcrRequestSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: "Invalid request", details: validation.error.flatten() },
        { status: 400 }
      );
    }

    const { image_source, model_name, api_key, api_base } = validation.data;

    // 2. Dynamically configure the AI model based on user input
    const openai = createOpenAI({
      apiKey: api_key || process.env.OPENAI_API_KEY,
      baseURL: api_base,
    });
    const model = openai(model_name);

    // 3. Use the Vercel AI SDK's `generateObject`
    const { object } = await generateObject({
      model: model,
      schema: CodeOcrResponseSchema,
      messages: [
        {
          role: "user",
          content: [
            { type: "text", text: CODE_OCR_PROMPT },
            { type: "image", image: image_source },
          ],
        },
      ],
    });

    // 4. Return the structured, validated response
    return NextResponse.json(object);
  } catch (error: any) {
    console.error("Error in CodeOCR API route:", error);
    return NextResponse.json(
      {
        error: "An unexpected error occurred.",
        details: error.message || "Unknown error",
      },
      { status: 500 }
    );
  }
}
